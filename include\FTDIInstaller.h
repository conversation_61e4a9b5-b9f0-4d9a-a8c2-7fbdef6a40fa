#ifndef FTDI_INSTALLER_H
#define FTDI_INSTALLER_H

#include <windows.h>
#include <string>
#include <vector>
#include "RS485Types.h"

/**
 * @brief FTDI Driver Installation and Management
 * 
 * This class handles the installation and management of FTDI VCP drivers
 * embedded within the RS485 application. It provides functionality to:
 * - Check if FTDI drivers are already installed
 * - Extract embedded driver files from application resources
 * - Install FTDI drivers programmatically
 * - Detect FTDI devices and enumerate COM ports
 */
class FTDIInstaller {
public:
    /**
     * @brief Installation result codes
     */
    enum class InstallResult {
        SUCCESS,                    // Driver installed successfully
        ALREADY_INSTALLED,          // Driver already present and working
        EXTRACTION_FAILED,          // Failed to extract embedded files
        INSTALLATION_FAILED,        // Driver installation failed
        INSUFFICIENT_PRIVILEGES,    // Need administrator rights
        DEVICE_NOT_FOUND,          // No FTDI device detected
        UNKNOWN_ERROR              // Unexpected error occurred
    };

    /**
     * @brief FTDI device information
     */
    struct FTDIDeviceInfo {
        std::wstring devicePath;
        std::wstring description;
        std::wstring serialNumber;
        std::wstring comPort;
        uint32_t vendorId;
        uint32_t productId;
        bool isDriverLoaded;
        bool isPortAvailable;
    };

    /**
     * @brief Install FTDI driver if needed
     * @return Installation result
     */
    static InstallResult InstallFTDIDriver();

    /**
     * @brief Check if FTDI driver is installed and working
     * @return true if driver is properly installed
     */
    static bool IsFTDIDriverInstalled();

    /**
     * @brief Extract embedded FTDI driver files to temporary directory
     * @return true if extraction successful
     */
    static bool ExtractEmbeddedFiles();

    /**
     * @brief Get temporary directory path for driver files
     * @return Wide string path to temporary directory
     */
    static std::wstring GetTempDirectory();

    /**
     * @brief Enumerate all FTDI devices in the system
     * @param devices Output vector to store device information
     * @return true if enumeration successful
     */
    static bool EnumerateFTDIDevices(std::vector<FTDIDeviceInfo>& devices);

    /**
     * @brief Find available COM port for FTDI device
     * @param vendorId USB vendor ID (default: 0x0403 for FTDI)
     * @param productId USB product ID (default: 0x6001 for FT232)
     * @return COM port name (e.g., "COM3") or empty string if not found
     */
    static std::wstring FindFTDIComPort(uint32_t vendorId = 0x0403, uint32_t productId = 0x6001);

    /**
     * @brief Check if running with administrator privileges
     * @return true if running as administrator
     */
    static bool CheckAdminPrivileges();

    /**
     * @brief Clean up temporary driver files
     * @return true if cleanup successful
     */
    static bool CleanupTempFiles();

    /**
     * @brief Get FTDI driver version information
     * @param version Output string for version information
     * @return true if version retrieved successfully
     */
    static bool GetFTDIDriverVersion(std::wstring& version);

    /**
     * @brief Convert InstallResult to human-readable string
     * @param result Installation result code
     * @return Descriptive string
     */
    static std::wstring InstallResultToString(InstallResult result);

private:
    /**
     * @brief Extract a single resource to file
     * @param resourceId Resource identifier
     * @param outputPath Output file path
     * @return true if extraction successful
     */
    static bool ExtractResource(int resourceId, const std::wstring& outputPath);

    /**
     * @brief Install driver from INF file
     * @param infPath Path to INF file
     * @return true if installation successful
     */
    static bool InstallDriverFromINF(const std::wstring& infPath);

    /**
     * @brief Check if specific FTDI device is present
     * @param vendorId USB vendor ID
     * @param productId USB product ID
     * @return true if device found
     */
    static bool IsFTDIDevicePresent(uint32_t vendorId = 0x0403, uint32_t productId = 0x6001);

    /**
     * @brief Get device property string
     * @param deviceInfoSet Device information set handle
     * @param deviceInfoData Device information data
     * @param property Property to retrieve
     * @param value Output string for property value
     * @return true if property retrieved successfully
     */
    static bool GetDeviceProperty(HDEVINFO deviceInfoSet, 
                                 SP_DEVINFO_DATA* deviceInfoData,
                                 DWORD property, 
                                 std::wstring& value);

    /**
     * @brief Get COM port name for device
     * @param deviceInfoSet Device information set handle
     * @param deviceInfoData Device information data
     * @param comPort Output string for COM port name
     * @return true if COM port found
     */
    static bool GetDeviceComPort(HDEVINFO deviceInfoSet, 
                                SP_DEVINFO_DATA* deviceInfoData,
                                std::wstring& comPort);

    /**
     * @brief Check if device driver is properly loaded
     * @param deviceInfoSet Device information set handle
     * @param deviceInfoData Device information data
     * @return true if driver is loaded and working
     */
    static bool IsDeviceDriverLoaded(HDEVINFO deviceInfoSet, 
                                    SP_DEVINFO_DATA* deviceInfoData);

    // Constants
    static constexpr uint32_t FTDI_VENDOR_ID = 0x0403;
    static constexpr uint32_t FTDI_FT232_PRODUCT_ID = 0x6001;
    static constexpr uint32_t FTDI_FT232H_PRODUCT_ID = 0x6014;
    static constexpr wchar_t TEMP_DIR_NAME[] = L"RS485_FTDI_Temp";
    static constexpr wchar_t FTDI_HARDWARE_ID[] = L"USB\\VID_0403&PID_6001";
};

#endif // FTDI_INSTALLER_H
