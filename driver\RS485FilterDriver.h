#ifndef RS485_FILTER_DRIVER_H
#define RS485_FILTER_DRIVER_H

#include <windows.h>
#include <setupapi.h>
#include <cfgmgr32.h>
#include "RS485Types.h"

// Simplified UMDF types for compilation
typedef <PERSON>AN<PERSON><PERSON> WDFSPINLOCK;
typedef <PERSON><PERSON><PERSON><PERSON> WDFWORKITEM;
typedef HANDLE WDFTIMER;
typedef HANDLE WDFQUEUE;
typedef HANDLE WDFREQUEST;
typedef HANDLE WDFDRIVER;
typedef HANDLE WDFDEVICE;

// Forward declarations for simplified compilation
class IWDFDevice;
class IWDFIoQueue;
class IWDFIoRequest;
class IWDFDriver;
class IWDFDeviceInitialize;
class IWDFFile;
class IWDFMemory;

/**
 * @brief UMDF 2.0 Filter Driver for RS485 Communication
 * 
 * This driver sits above the FTDI VCP driver and provides:
 * - ZES protocol processing
 * - Advanced buffer management
 * - DeviceIoControl interface for applications
 * - Automatic frame validation and CRC checking
 */

// IOCTL codes for communication with user applications
#define IOCTL_RS485_CONFIGURE_SYSTEM    CTL_CODE(FILE_DEVICE_UNKNOWN, 0x800, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_RS485_CONFIGURE_USER      CTL_CODE(FILE_DEVICE_UNKNOWN, 0x801, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_RS485_REQUEST_DATA        CTL_CODE(FILE_DEVICE_UNKNOWN, 0x802, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_RS485_RECEIVE_RESPONSE    CTL_CODE(FILE_DEVICE_UNKNOWN, 0x803, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_RS485_GET_BUFFER_STATUS   CTL_CODE(FILE_DEVICE_UNKNOWN, 0x804, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_RS485_CLEAR_BUFFER        CTL_CODE(FILE_DEVICE_UNKNOWN, 0x805, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_RS485_GET_HARDWARE_STATUS CTL_CODE(FILE_DEVICE_UNKNOWN, 0x806, METHOD_BUFFERED, FILE_ANY_ACCESS)
#define IOCTL_RS485_MODEL_DATA_OP       CTL_CODE(FILE_DEVICE_UNKNOWN, 0x807, METHOD_BUFFERED, FILE_ANY_ACCESS)

// Input/Output structures for IOCTL calls
typedef struct _RS485_COMMAND_INPUT {
    CHAR CommandKey[4];     // Command identifier (e.g., "S001", "U001")
    UINT64 Value;           // Command value
    UINT8 SlaveAddress;     // Target slave address (0 for broadcast)
    UINT8 Reserved[3];      // Padding for alignment
} RS485_COMMAND_INPUT, *PRS485_COMMAND_INPUT;

typedef struct _RS485_REQUEST_INPUT {
    CHAR DataKey[4];        // Data identifier (e.g., "A001", "A002")
    UINT8 SlaveAddress;     // Target slave address
    UINT32 Timeout;         // Timeout in milliseconds
    UINT8 Reserved[3];      // Padding for alignment
} RS485_REQUEST_INPUT, *PRS485_REQUEST_INPUT;

typedef struct _RS485_RESPONSE_OUTPUT {
    UINT8 SlaveAddress;     // Source slave address
    UINT8 PayloadData[12];  // Response payload data
    UINT32 DataLength;      // Actual data length
    UINT8 Reserved[3];      // Padding for alignment
} RS485_RESPONSE_OUTPUT, *PRS485_RESPONSE_OUTPUT;

typedef struct _RS485_MODEL_DATA_INPUT {
    UINT8 SlaveAddress;     // Target slave address
    UINT32 Address;         // Memory address in FRAM
    UINT32 Length;          // Data length
    BOOLEAN IsWrite;        // TRUE for write, FALSE for read
    UINT8 Data[256];        // Data buffer (for write operations)
} RS485_MODEL_DATA_INPUT, *PRS485_MODEL_DATA_INPUT;

// Forward declarations
class CRS485FilterDriver;
class CRS485Device;
class CRS485Queue;

/**
 * @brief Main filter driver class
 */
class CRS485FilterDriver : public CUnknown
{
private:
    // Reference count for COM interface
    LONG m_cRef;

public:
    // Constructor/Destructor
    CRS485FilterDriver();
    virtual ~CRS485FilterDriver();

    // IUnknown methods
    STDMETHOD(QueryInterface)(REFIID riid, void** ppvObject);
    STDMETHOD_(ULONG, AddRef)();
    STDMETHOD_(ULONG, Release)();

    // Driver entry points
    static HRESULT CreateInstance(IWDFDriver* pDriver, IWDFDeviceInitialize* pDeviceInit);
    
    // Device management
    HRESULT OnDeviceAdd(IWDFDriver* pDriver, IWDFDeviceInitialize* pDeviceInit);
    
private:
    // Helper methods
    HRESULT ConfigureDevice(IWDFDevice* pDevice);
    HRESULT CreateQueues(IWDFDevice* pDevice);
};

/**
 * @brief Device object class
 */
class CRS485Device : public CUnknown
{
private:
    LONG m_cRef;
    IWDFDevice* m_pWdfDevice;
    IWDFFile* m_pSerialFile;
    
    // Buffer management
    WDFSPINLOCK m_BufferLock;
    LIST_ENTRY m_UplinkBufferHead;
    LIST_ENTRY m_DownlinkBufferHead;
    ULONG m_UplinkBufferCount;
    ULONG m_DownlinkBufferCount;
    
    // Frame processing
    WDFWORKITEM m_FrameProcessorWorkItem;
    WDFTIMER m_ResponseTimer;

public:
    CRS485Device();
    virtual ~CRS485Device();

    // IUnknown methods
    STDMETHOD(QueryInterface)(REFIID riid, void** ppvObject);
    STDMETHOD_(ULONG, AddRef)();
    STDMETHOD_(ULONG, Release)();

    // Initialization
    HRESULT Initialize(IWDFDevice* pDevice);
    HRESULT Configure();
    void Cleanup();

    // Buffer management
    HRESULT AddToUplinkBuffer(const UINT8* pData, ULONG DataLength);
    HRESULT GetFromDownlinkBuffer(UINT8* pData, ULONG* pDataLength);
    HRESULT ClearBuffers(BufferType BufferType);
    HRESULT GetBufferStatus(BufferStatus* pStatus);

    // Frame processing
    HRESULT SendFrame(const RS485Frame* pFrame);
    HRESULT ProcessReceivedFrame(const RS485Frame* pFrame);
    
    // Command processing
    HRESULT ProcessSystemCommand(const RS485_COMMAND_INPUT* pInput);
    HRESULT ProcessUserCommand(const RS485_COMMAND_INPUT* pInput);
    HRESULT ProcessDataRequest(const RS485_REQUEST_INPUT* pInput);
    HRESULT ProcessModelDataOperation(const RS485_MODEL_DATA_INPUT* pInput);

    // Callbacks
    static VOID FrameProcessorWorkItemCallback(WDFWORKITEM WorkItem);
    static VOID ResponseTimerCallback(WDFTIMER Timer);

private:
    // Helper methods
    HRESULT OpenSerialPort();
    HRESULT CloseSerialPort();
    HRESULT ValidateFrame(const RS485Frame* pFrame);
    UINT8 CreateIdByte(FunctionCode FunctionCode, UINT8 DeviceAddress);
    FunctionCode ExtractFunctionCode(UINT8 IdByte);
    UINT8 ExtractDeviceAddress(UINT8 IdByte);
};

/**
 * @brief I/O Queue class for handling IOCTL requests
 */
class CRS485Queue : public CUnknown
{
private:
    LONG m_cRef;
    IWDFIoQueue* m_pWdfQueue;
    CRS485Device* m_pDevice;

public:
    CRS485Queue();
    virtual ~CRS485Queue();

    // IUnknown methods
    STDMETHOD(QueryInterface)(REFIID riid, void** ppvObject);
    STDMETHOD_(ULONG, AddRef)();
    STDMETHOD_(ULONG, Release)();

    // Initialization
    HRESULT Initialize(IWDFDevice* pDevice, CRS485Device* pRS485Device);

    // Queue callbacks
    STDMETHOD_(void, OnDeviceIoControl)(
        IWDFIoQueue* pWdfQueue,
        IWDFIoRequest* pWdfRequest,
        ULONG ControlCode,
        SIZE_T InputBufferSizeInBytes,
        SIZE_T OutputBufferSizeInBytes
    );

private:
    // IOCTL handlers
    HRESULT HandleConfigureSystem(IWDFIoRequest* pRequest);
    HRESULT HandleConfigureUser(IWDFIoRequest* pRequest);
    HRESULT HandleRequestData(IWDFIoRequest* pRequest);
    HRESULT HandleReceiveResponse(IWDFIoRequest* pRequest);
    HRESULT HandleGetBufferStatus(IWDFIoRequest* pRequest);
    HRESULT HandleClearBuffer(IWDFIoRequest* pRequest);
    HRESULT HandleGetHardwareStatus(IWDFIoRequest* pRequest);
    HRESULT HandleModelDataOperation(IWDFIoRequest* pRequest);
    
    // Helper methods
    HRESULT GetInputBuffer(IWDFIoRequest* pRequest, void** ppBuffer, SIZE_T* pBufferSize);
    HRESULT GetOutputBuffer(IWDFIoRequest* pRequest, void** ppBuffer, SIZE_T* pBufferSize);
    HRESULT CompleteRequest(IWDFIoRequest* pRequest, HRESULT hr, SIZE_T Information = 0);
};

// Global driver instance
extern CRS485FilterDriver* g_pRS485Driver;

// Driver entry point
extern "C" HRESULT WINAPI DllMain(HINSTANCE hInstance, DWORD dwReason, LPVOID lpReserved);

// WDF driver entry point
extern "C" NTSTATUS DriverEntry(PDRIVER_OBJECT DriverObject, PUNICODE_STRING RegistryPath);

#endif // RS485_FILTER_DRIVER_H
