#include "RS485FilterDriver.h"
#include "CRC8Calculator.h"
#include "RS485DataFormat.h"
#include <windows.h>
#include <setupapi.h>
#include <cfgmgr32.h>
#include <iostream>

// Simplified UMDF simulation for compilation
// In a real WDK environment, these would be actual UMDF headers
typedef HANDLE WDFSPINLOCK;
typedef HANDLE WDFWORKITEM;
typedef HANDLE WDFTIMER;
typedef HANDLE WDFQUEUE;
typedef HANDLE WDFREQUEST;
typedef HANDLE WDFDRIVER;
typedef HANDLE WDFDEVICE;

typedef struct _LIST_ENTRY {
    struct _LIST_ENTRY* Flink;
    struct _LIST_ENTRY* Blink;
} LIST_ENTRY, *PLIST_ENTRY;

// Mock WDF functions for compilation
NTSTATUS WdfSpinLockCreate(void* attr, WDFSPINLOCK* lock) { *lock = CreateMutex(NULL, FALSE, NULL); return 0; }
void WdfSpinLockAcquire(WDFSPIN<PERSON>OC<PERSON> lock) { WaitForSingleObject(lock, INFINITE); }
void WdfSpinLockRelease(WDF<PERSON>INLOCK lock) { ReleaseMutex(lock); }
NTSTATUS WdfWorkItemCreate(void* config, void* attr, WDFWORKITEM* item) { *item = NULL; return 0; }
NTSTATUS WdfTimerCreate(void* config, void* attr, WDFTIMER* timer) { *timer = NULL; return 0; }
void InitializeListHead(PLIST_ENTRY head) { head->Flink = head->Blink = head; }
BOOLEAN IsListEmpty(PLIST_ENTRY head) { return head->Flink == head; }
PLIST_ENTRY RemoveHeadList(PLIST_ENTRY head) {
    PLIST_ENTRY entry = head->Flink;
    head->Flink = entry->Flink;
    entry->Flink->Blink = head;
    return entry;
}
void InsertTailList(PLIST_ENTRY head, PLIST_ENTRY entry) {
    entry->Flink = head;
    entry->Blink = head->Blink;
    head->Blink->Flink = entry;
    head->Blink = entry;
}
void* ExAllocatePoolWithTag(int type, size_t size, ULONG tag) {
    UNREFERENCED_PARAMETER(type); UNREFERENCED_PARAMETER(tag);
    return malloc(size);
}
void ExFreePoolWithTag(void* ptr, ULONG tag) {
    UNREFERENCED_PARAMETER(tag);
    free(ptr);
}
void RtlCopyMemory(void* dest, const void* src, size_t size) { memcpy(dest, src, size); }
void RtlZeroMemory(void* dest, size_t size) { memset(dest, 0, size); }

#define NonPagedPool 0
#define NT_SUCCESS(status) ((status) >= 0)
#define STATUS_SUCCESS 0L
#define NTSTATUS LONG

// Global driver instance
CRS485FilterDriver* g_pRS485Driver = nullptr;

/**
 * @brief Driver entry point
 */
extern "C" NTSTATUS DriverEntry(PDRIVER_OBJECT DriverObject, PUNICODE_STRING RegistryPath)
{
    UNREFERENCED_PARAMETER(DriverObject);
    UNREFERENCED_PARAMETER(RegistryPath);

    // Initialize WDF
    WDF_DRIVER_CONFIG driverConfig;
    WDF_DRIVER_CONFIG_INIT(&driverConfig, nullptr);
    
    // Set driver callbacks
    driverConfig.EvtDriverDeviceAdd = [](WDFDRIVER Driver, PWDFDEVICE_INIT DeviceInit) -> NTSTATUS {
        UNREFERENCED_PARAMETER(Driver);
        
        // Create device
        WDFDEVICE device;
        NTSTATUS status = WdfDeviceCreate(&DeviceInit, WDF_NO_OBJECT_ATTRIBUTES, &device);
        if (!NT_SUCCESS(status)) {
            return status;
        }
        
        // Initialize our driver
        if (g_pRS485Driver) {
            // This would be implemented with proper UMDF interfaces
            // For now, return success
        }
        
        return STATUS_SUCCESS;
    };

    WDFDRIVER driver;
    NTSTATUS status = WdfDriverCreate(DriverObject, RegistryPath, WDF_NO_OBJECT_ATTRIBUTES, &driverConfig, &driver);
    
    return status;
}

/**
 * @brief DLL entry point
 */
extern "C" HRESULT WINAPI DllMain(HINSTANCE hInstance, DWORD dwReason, LPVOID lpReserved)
{
    UNREFERENCED_PARAMETER(hInstance);
    UNREFERENCED_PARAMETER(lpReserved);

    switch (dwReason) {
        case DLL_PROCESS_ATTACH:
            // Initialize driver instance
            g_pRS485Driver = new CRS485FilterDriver();
            if (!g_pRS485Driver) {
                return E_OUTOFMEMORY;
            }
            break;

        case DLL_PROCESS_DETACH:
            // Cleanup driver instance
            if (g_pRS485Driver) {
                g_pRS485Driver->Release();
                g_pRS485Driver = nullptr;
            }
            break;
    }

    return S_OK;
}

// ===== CRS485FilterDriver Implementation =====

CRS485FilterDriver::CRS485FilterDriver() : m_cRef(1)
{
}

CRS485FilterDriver::~CRS485FilterDriver()
{
}

STDMETHODIMP CRS485FilterDriver::QueryInterface(REFIID riid, void** ppvObject)
{
    if (!ppvObject) {
        return E_INVALIDARG;
    }

    *ppvObject = nullptr;

    if (IsEqualIID(riid, IID_IUnknown)) {
        *ppvObject = static_cast<IUnknown*>(this);
        AddRef();
        return S_OK;
    }

    return E_NOINTERFACE;
}

STDMETHODIMP_(ULONG) CRS485FilterDriver::AddRef()
{
    return InterlockedIncrement(&m_cRef);
}

STDMETHODIMP_(ULONG) CRS485FilterDriver::Release()
{
    LONG cRef = InterlockedDecrement(&m_cRef);
    if (cRef == 0) {
        delete this;
    }
    return cRef;
}

HRESULT CRS485FilterDriver::CreateInstance(IWDFDriver* pDriver, IWDFDeviceInitialize* pDeviceInit)
{
    UNREFERENCED_PARAMETER(pDriver);
    UNREFERENCED_PARAMETER(pDeviceInit);

    // This would create and configure the device instance
    // For now, return success
    return S_OK;
}

HRESULT CRS485FilterDriver::OnDeviceAdd(IWDFDriver* pDriver, IWDFDeviceInitialize* pDeviceInit)
{
    UNREFERENCED_PARAMETER(pDriver);

    // Set device properties
    pDeviceInit->SetLockingConstraint(None);
    pDeviceInit->SetPowerPolicyOwnership(FALSE);

    // Create device object
    IWDFDevice* pDevice = nullptr;
    HRESULT hr = pDriver->CreateDevice(pDeviceInit, nullptr, &pDevice);
    if (FAILED(hr)) {
        return hr;
    }

    // Configure device
    hr = ConfigureDevice(pDevice);
    if (FAILED(hr)) {
        pDevice->Release();
        return hr;
    }

    // Create I/O queues
    hr = CreateQueues(pDevice);
    if (FAILED(hr)) {
        pDevice->Release();
        return hr;
    }

    pDevice->Release();
    return S_OK;
}

HRESULT CRS485FilterDriver::ConfigureDevice(IWDFDevice* pDevice)
{
    UNREFERENCED_PARAMETER(pDevice);

    // Configure device-specific settings
    // This would include setting up the filter driver to sit above FTDI VCP
    
    return S_OK;
}

HRESULT CRS485FilterDriver::CreateQueues(IWDFDevice* pDevice)
{
    // Create default I/O queue for handling IOCTL requests
    IWDFIoQueue* pQueue = nullptr;
    
    WDF_IO_QUEUE_CONFIG queueConfig;
    WDF_IO_QUEUE_CONFIG_INIT_DEFAULT_QUEUE(&queueConfig, WdfIoQueueDispatchParallel);
    
    // Set queue callbacks
    queueConfig.EvtIoDeviceControl = [](WDFQUEUE Queue, WDFREQUEST Request, 
                                       size_t OutputBufferLength, size_t InputBufferLength, 
                                       ULONG IoControlCode) {
        UNREFERENCED_PARAMETER(Queue);
        UNREFERENCED_PARAMETER(OutputBufferLength);
        UNREFERENCED_PARAMETER(InputBufferLength);
        
        // Handle IOCTL requests
        NTSTATUS status = STATUS_SUCCESS;
        
        switch (IoControlCode) {
            case IOCTL_RS485_CONFIGURE_SYSTEM:
                // Handle system configuration
                break;
                
            case IOCTL_RS485_CONFIGURE_USER:
                // Handle user configuration
                break;
                
            case IOCTL_RS485_REQUEST_DATA:
                // Handle data requests
                break;
                
            case IOCTL_RS485_RECEIVE_RESPONSE:
                // Handle response reception
                break;
                
            case IOCTL_RS485_GET_BUFFER_STATUS:
                // Handle buffer status queries
                break;
                
            case IOCTL_RS485_CLEAR_BUFFER:
                // Handle buffer clearing
                break;
                
            case IOCTL_RS485_GET_HARDWARE_STATUS:
                // Handle hardware status queries
                break;
                
            case IOCTL_RS485_MODEL_DATA_OP:
                // Handle model data operations
                break;
                
            default:
                status = STATUS_INVALID_DEVICE_REQUEST;
                break;
        }
        
        WdfRequestComplete(Request, status);
    };

    WDFQUEUE queue;
    NTSTATUS status = WdfIoQueueCreate(WdfDeviceGetDefaultQueue(pDevice), 
                                      &queueConfig, WDF_NO_OBJECT_ATTRIBUTES, &queue);
    
    return HRESULT_FROM_NT(status);
}

// ===== CRS485Device Implementation =====

CRS485Device::CRS485Device() : 
    m_cRef(1),
    m_pWdfDevice(nullptr),
    m_pSerialFile(nullptr),
    m_UplinkBufferCount(0),
    m_DownlinkBufferCount(0)
{
    InitializeListHead(&m_UplinkBufferHead);
    InitializeListHead(&m_DownlinkBufferHead);
}

CRS485Device::~CRS485Device()
{
    Cleanup();
}

STDMETHODIMP CRS485Device::QueryInterface(REFIID riid, void** ppvObject)
{
    if (!ppvObject) {
        return E_INVALIDARG;
    }

    *ppvObject = nullptr;

    if (IsEqualIID(riid, IID_IUnknown)) {
        *ppvObject = static_cast<IUnknown*>(this);
        AddRef();
        return S_OK;
    }

    return E_NOINTERFACE;
}

STDMETHODIMP_(ULONG) CRS485Device::AddRef()
{
    return InterlockedIncrement(&m_cRef);
}

STDMETHODIMP_(ULONG) CRS485Device::Release()
{
    LONG cRef = InterlockedDecrement(&m_cRef);
    if (cRef == 0) {
        delete this;
    }
    return cRef;
}

HRESULT CRS485Device::Initialize(IWDFDevice* pDevice)
{
    if (!pDevice) {
        return E_INVALIDARG;
    }

    m_pWdfDevice = pDevice;
    m_pWdfDevice->AddRef();

    // Initialize spinlock for buffer protection
    WDF_OBJECT_ATTRIBUTES attributes;
    WDF_OBJECT_ATTRIBUTES_INIT(&attributes);
    
    NTSTATUS status = WdfSpinLockCreate(&attributes, &m_BufferLock);
    if (!NT_SUCCESS(status)) {
        return HRESULT_FROM_NT(status);
    }

    // Create work item for frame processing
    WDF_WORKITEM_CONFIG workItemConfig;
    WDF_WORKITEM_CONFIG_INIT(&workItemConfig, FrameProcessorWorkItemCallback);
    
    status = WdfWorkItemCreate(&workItemConfig, &attributes, &m_FrameProcessorWorkItem);
    if (!NT_SUCCESS(status)) {
        return HRESULT_FROM_NT(status);
    }

    // Create timer for response timeout handling
    WDF_TIMER_CONFIG timerConfig;
    WDF_TIMER_CONFIG_INIT(&timerConfig, ResponseTimerCallback);
    
    status = WdfTimerCreate(&timerConfig, &attributes, &m_ResponseTimer);
    if (!NT_SUCCESS(status)) {
        return HRESULT_FROM_NT(status);
    }

    return S_OK;
}

HRESULT CRS485Device::Configure()
{
    // Open connection to underlying serial port (FTDI VCP)
    return OpenSerialPort();
}

void CRS485Device::Cleanup()
{
    CloseSerialPort();
    
    if (m_pWdfDevice) {
        m_pWdfDevice->Release();
        m_pWdfDevice = nullptr;
    }
    
    // Clear buffers
    ClearBuffers(BufferType::BOTH);
}

HRESULT CRS485Device::OpenSerialPort()
{
    // This would open a handle to the underlying FTDI VCP driver
    // For now, return success
    return S_OK;
}

HRESULT CRS485Device::CloseSerialPort()
{
    if (m_pSerialFile) {
        m_pSerialFile->Release();
        m_pSerialFile = nullptr;
    }

    return S_OK;
}

HRESULT CRS485Device::AddToUplinkBuffer(const UINT8* pData, ULONG DataLength)
{
    if (!pData || DataLength != RS485_PAYLOAD_SIZE) {
        return E_INVALIDARG;
    }

    WdfSpinLockAcquire(m_BufferLock);

    // Check buffer capacity
    if (m_UplinkBufferCount >= RS485_UPLINK_BUFFER_SIZE) {
        WdfSpinLockRelease(m_BufferLock);
        return HRESULT_FROM_WIN32(ERROR_INSUFFICIENT_BUFFER);
    }

    // Allocate buffer entry
    PLIST_ENTRY pEntry = (PLIST_ENTRY)ExAllocatePoolWithTag(NonPagedPool,
                                                            sizeof(LIST_ENTRY) + DataLength,
                                                            'U485');
    if (!pEntry) {
        WdfSpinLockRelease(m_BufferLock);
        return E_OUTOFMEMORY;
    }

    // Copy data
    UINT8* pBufferData = (UINT8*)(pEntry + 1);
    RtlCopyMemory(pBufferData, pData, DataLength);

    // Add to list
    InsertTailList(&m_UplinkBufferHead, pEntry);
    m_UplinkBufferCount++;

    WdfSpinLockRelease(m_BufferLock);
    return S_OK;
}

HRESULT CRS485Device::GetFromDownlinkBuffer(UINT8* pData, ULONG* pDataLength)
{
    if (!pData || !pDataLength) {
        return E_INVALIDARG;
    }

    WdfSpinLockAcquire(m_BufferLock);

    // Check if buffer is empty
    if (IsListEmpty(&m_DownlinkBufferHead)) {
        WdfSpinLockRelease(m_BufferLock);
        *pDataLength = 0;
        return S_FALSE; // No data available
    }

    // Get first entry
    PLIST_ENTRY pEntry = RemoveHeadList(&m_DownlinkBufferHead);
    m_DownlinkBufferCount--;

    WdfSpinLockRelease(m_BufferLock);

    // Copy data
    UINT8* pBufferData = (UINT8*)(pEntry + 1);
    RtlCopyMemory(pData, pBufferData, RS485_PAYLOAD_SIZE);
    *pDataLength = RS485_PAYLOAD_SIZE;

    // Free buffer entry
    ExFreePoolWithTag(pEntry, 'D485');

    return S_OK;
}

HRESULT CRS485Device::ClearBuffers(BufferType BufferType)
{
    WdfSpinLockAcquire(m_BufferLock);

    if (BufferType == BufferType::UPLINK || BufferType == BufferType::BOTH) {
        while (!IsListEmpty(&m_UplinkBufferHead)) {
            PLIST_ENTRY pEntry = RemoveHeadList(&m_UplinkBufferHead);
            ExFreePoolWithTag(pEntry, 'U485');
        }
        m_UplinkBufferCount = 0;
    }

    if (BufferType == BufferType::DOWNLINK || BufferType == BufferType::BOTH) {
        while (!IsListEmpty(&m_DownlinkBufferHead)) {
            PLIST_ENTRY pEntry = RemoveHeadList(&m_DownlinkBufferHead);
            ExFreePoolWithTag(pEntry, 'D485');
        }
        m_DownlinkBufferCount = 0;
    }

    WdfSpinLockRelease(m_BufferLock);
    return S_OK;
}

HRESULT CRS485Device::GetBufferStatus(BufferStatus* pStatus)
{
    if (!pStatus) {
        return E_INVALIDARG;
    }

    WdfSpinLockAcquire(m_BufferLock);

    pStatus->uplinkUsed = m_UplinkBufferCount;
    pStatus->uplinkCapacity = RS485_UPLINK_BUFFER_SIZE;
    pStatus->downlinkUsed = m_DownlinkBufferCount;
    pStatus->downlinkCapacity = RS485_DOWNLINK_BUFFER_SIZE;

    pStatus->isUplinkFull = (m_UplinkBufferCount >= RS485_UPLINK_BUFFER_SIZE);
    pStatus->isDownlinkFull = (m_DownlinkBufferCount >= RS485_DOWNLINK_BUFFER_SIZE);

    pStatus->uplinkUsagePercent = (double)m_UplinkBufferCount / RS485_UPLINK_BUFFER_SIZE * 100.0;
    pStatus->downlinkUsagePercent = (double)m_DownlinkBufferCount / RS485_DOWNLINK_BUFFER_SIZE * 100.0;

    WdfSpinLockRelease(m_BufferLock);
    return S_OK;
}

HRESULT CRS485Device::SendFrame(const RS485Frame* pFrame)
{
    if (!pFrame) {
        return E_INVALIDARG;
    }

    if (!m_pSerialFile) {
        return HRESULT_FROM_WIN32(ERROR_DEVICE_NOT_CONNECTED);
    }

    // Validate frame
    HRESULT hr = ValidateFrame(pFrame);
    if (FAILED(hr)) {
        return hr;
    }

    // Send frame to underlying serial port
    // This would use IWDFFile::Write to send data to FTDI VCP driver
    // For now, we'll simulate success

    return S_OK;
}

HRESULT CRS485Device::ProcessReceivedFrame(const RS485Frame* pFrame)
{
    if (!pFrame) {
        return E_INVALIDARG;
    }

    // Validate frame
    HRESULT hr = ValidateFrame(pFrame);
    if (FAILED(hr)) {
        return hr;
    }

    // Extract function code and device address
    FunctionCode functionCode = ExtractFunctionCode(pFrame->id_byte);
    UINT8 deviceAddress = ExtractDeviceAddress(pFrame->id_byte);

    // Process based on function code
    switch (functionCode) {
        case FunctionCode::RESPONSE_ASSIGN:
        case FunctionCode::RESPONSE_REQUEST:
            // Store response in downlink buffer
            hr = AddToDownlinkBuffer(pFrame->payload, RS485_PAYLOAD_SIZE);
            break;

        case FunctionCode::RESEND_REQUEST:
            // Handle resend request
            // This would trigger a retry of the last sent frame
            break;

        default:
            // Unexpected function code for received frame
            hr = E_UNEXPECTED;
            break;
    }

    return hr;
}

HRESULT CRS485Device::AddToDownlinkBuffer(const UINT8* pData, ULONG DataLength)
{
    if (!pData || DataLength != RS485_PAYLOAD_SIZE) {
        return E_INVALIDARG;
    }

    WdfSpinLockAcquire(m_BufferLock);

    // Check buffer capacity
    if (m_DownlinkBufferCount >= RS485_DOWNLINK_BUFFER_SIZE) {
        WdfSpinLockRelease(m_BufferLock);
        return HRESULT_FROM_WIN32(ERROR_INSUFFICIENT_BUFFER);
    }

    // Allocate buffer entry
    PLIST_ENTRY pEntry = (PLIST_ENTRY)ExAllocatePoolWithTag(NonPagedPool,
                                                            sizeof(LIST_ENTRY) + DataLength,
                                                            'D485');
    if (!pEntry) {
        WdfSpinLockRelease(m_BufferLock);
        return E_OUTOFMEMORY;
    }

    // Copy data
    UINT8* pBufferData = (UINT8*)(pEntry + 1);
    RtlCopyMemory(pBufferData, pData, DataLength);

    // Add to list
    InsertTailList(&m_DownlinkBufferHead, pEntry);
    m_DownlinkBufferCount++;

    WdfSpinLockRelease(m_BufferLock);
    return S_OK;
}

HRESULT CRS485Device::ProcessSystemCommand(const RS485_COMMAND_INPUT* pInput)
{
    if (!pInput) {
        return E_INVALIDARG;
    }

    // Validate command key
    if (strncmp(pInput->CommandKey, "S", 1) != 0) {
        return E_INVALIDARG;
    }

    // Create RS485 frame
    RS485Frame frame = {};
    frame.header = RS485_FRAME_HEADER;
    frame.trailer = RS485_FRAME_TRAILER;
    frame.id_byte = CreateIdByte(FunctionCode::ASSIGN_DATA, 0); // Broadcast

    // Copy command key and value to payload
    RS485DataFormat::createPayload(pInput->CommandKey, pInput->Value, frame.payload);

    // Calculate CRC
    frame.crc8 = CRC8Calculator::calculateFrameCRC(&frame);

    // Send frame
    return SendFrame(&frame);
}

HRESULT CRS485Device::ProcessUserCommand(const RS485_COMMAND_INPUT* pInput)
{
    if (!pInput) {
        return E_INVALIDARG;
    }

    // Validate command key
    if (strncmp(pInput->CommandKey, "U", 1) != 0 && strncmp(pInput->CommandKey, "W", 1) != 0) {
        return E_INVALIDARG;
    }

    // Validate slave address
    if (pInput->SlaveAddress < 1 || pInput->SlaveAddress > 31) {
        return E_INVALIDARG;
    }

    // Create RS485 frame
    RS485Frame frame = {};
    frame.header = RS485_FRAME_HEADER;
    frame.trailer = RS485_FRAME_TRAILER;
    frame.id_byte = CreateIdByte(FunctionCode::ASSIGN_DATA, pInput->SlaveAddress);

    // Copy command key and value to payload
    RS485DataFormat::createPayload(pInput->CommandKey, pInput->Value, frame.payload);

    // Calculate CRC
    frame.crc8 = CRC8Calculator::calculateFrameCRC(&frame);

    // Send frame
    return SendFrame(&frame);
}

HRESULT CRS485Device::ProcessDataRequest(const RS485_REQUEST_INPUT* pInput)
{
    if (!pInput) {
        return E_INVALIDARG;
    }

    // Validate command key
    if (strncmp(pInput->DataKey, "A", 1) != 0) {
        return E_INVALIDARG;
    }

    // Validate slave address
    if (pInput->SlaveAddress < 1 || pInput->SlaveAddress > 31) {
        return E_INVALIDARG;
    }

    // Create RS485 frame
    RS485Frame frame = {};
    frame.header = RS485_FRAME_HEADER;
    frame.trailer = RS485_FRAME_TRAILER;
    frame.id_byte = CreateIdByte(FunctionCode::REQUEST_DATA, pInput->SlaveAddress);

    // Copy command key (value is 0 for requests)
    RS485DataFormat::createPayload(pInput->DataKey, 0, frame.payload);

    // Calculate CRC
    frame.crc8 = CRC8Calculator::calculateFrameCRC(&frame);

    // Send frame
    return SendFrame(&frame);
}

HRESULT CRS485Device::ProcessModelDataOperation(const RS485_MODEL_DATA_INPUT* pInput)
{
    if (!pInput) {
        return E_INVALIDARG;
    }

    // Validate slave address
    if (pInput->SlaveAddress < 1 || pInput->SlaveAddress > 31) {
        return E_INVALIDARG;
    }

    // Create RS485 frame
    RS485Frame frame = {};
    frame.header = RS485_FRAME_HEADER;
    frame.trailer = RS485_FRAME_TRAILER;
    frame.id_byte = CreateIdByte(FunctionCode::ASSIGN_DATA, pInput->SlaveAddress);

    // For W-series commands, encode address and data
    const char* commandKey = pInput->IsWrite ? "W001" : "W002";

    // Encode address in first 4 bytes of value, data in last 4 bytes
    UINT64 encodedValue = RS485DataFormat::encodeDualIntegers(pInput->Address,
                                                              *(UINT32*)pInput->Data);

    RS485DataFormat::createPayload(commandKey, encodedValue, frame.payload);

    // Calculate CRC
    frame.crc8 = CRC8Calculator::calculateFrameCRC(&frame);

    // Send frame
    return SendFrame(&frame);
}

HRESULT CRS485Device::ValidateFrame(const RS485Frame* pFrame)
{
    if (!pFrame) {
        return E_INVALIDARG;
    }

    // Check header and trailer
    if (pFrame->header != RS485_FRAME_HEADER || pFrame->trailer != RS485_FRAME_TRAILER) {
        return E_INVALIDARG;
    }

    // Validate CRC
    if (!CRC8Calculator::validateFrameCRC(pFrame)) {
        return HRESULT_FROM_WIN32(ERROR_CRC);
    }

    return S_OK;
}

UINT8 CRS485Device::CreateIdByte(FunctionCode FunctionCode, UINT8 DeviceAddress)
{
    UINT8 idByte = 0;
    idByte |= (static_cast<UINT8>(FunctionCode) & 0x07) << 5; // Function code in upper 3 bits
    idByte |= (DeviceAddress & 0x1F);                         // Device address in lower 5 bits
    return idByte;
}

FunctionCode CRS485Device::ExtractFunctionCode(UINT8 IdByte)
{
    return static_cast<FunctionCode>((IdByte >> 5) & 0x07);
}

UINT8 CRS485Device::ExtractDeviceAddress(UINT8 IdByte)
{
    return IdByte & 0x1F;
}

VOID CRS485Device::FrameProcessorWorkItemCallback(WDFWORKITEM WorkItem)
{
    UNREFERENCED_PARAMETER(WorkItem);

    // This callback would process incoming frames from the serial port
    // It would be triggered when data is available from the FTDI VCP driver

    // Implementation would:
    // 1. Read data from serial port
    // 2. Parse frames from byte stream
    // 3. Validate frames
    // 4. Process valid frames
    // 5. Store responses in downlink buffer
}

VOID CRS485Device::ResponseTimerCallback(WDFTIMER Timer)
{
    UNREFERENCED_PARAMETER(Timer);

    // This callback handles response timeouts
    // It would be triggered when a response is not received within the expected time

    // Implementation would:
    // 1. Check for pending requests
    // 2. Retry failed requests (up to max retry count)
    // 3. Complete requests with timeout error if max retries exceeded
}

// ===== CRS485Queue Implementation =====

CRS485Queue::CRS485Queue() :
    m_cRef(1),
    m_pWdfQueue(nullptr),
    m_pDevice(nullptr)
{
}

CRS485Queue::~CRS485Queue()
{
    if (m_pWdfQueue) {
        m_pWdfQueue->Release();
    }

    if (m_pDevice) {
        m_pDevice->Release();
    }
}

STDMETHODIMP CRS485Queue::QueryInterface(REFIID riid, void** ppvObject)
{
    if (!ppvObject) {
        return E_INVALIDARG;
    }

    *ppvObject = nullptr;

    if (IsEqualIID(riid, IID_IUnknown)) {
        *ppvObject = static_cast<IUnknown*>(this);
        AddRef();
        return S_OK;
    }

    return E_NOINTERFACE;
}

STDMETHODIMP_(ULONG) CRS485Queue::AddRef()
{
    return InterlockedIncrement(&m_cRef);
}

STDMETHODIMP_(ULONG) CRS485Queue::Release()
{
    LONG cRef = InterlockedDecrement(&m_cRef);
    if (cRef == 0) {
        delete this;
    }
    return cRef;
}

HRESULT CRS485Queue::Initialize(IWDFDevice* pDevice, CRS485Device* pRS485Device)
{
    if (!pDevice || !pRS485Device) {
        return E_INVALIDARG;
    }

    m_pDevice = pRS485Device;
    m_pDevice->AddRef();

    // Create default I/O queue
    IWDFIoQueue* pQueue = nullptr;
    HRESULT hr = pDevice->CreateIoQueue(nullptr, TRUE, WdfIoQueueDispatchParallel,
                                       TRUE, FALSE, &pQueue);
    if (FAILED(hr)) {
        return hr;
    }

    m_pWdfQueue = pQueue;

    // Set queue callbacks
    hr = pQueue->SetUMDFIoQueueConfig(this, nullptr, nullptr, nullptr);
    if (FAILED(hr)) {
        pQueue->Release();
        m_pWdfQueue = nullptr;
        return hr;
    }

    return S_OK;
}

STDMETHODIMP_(void) CRS485Queue::OnDeviceIoControl(
    IWDFIoQueue* pWdfQueue,
    IWDFIoRequest* pWdfRequest,
    ULONG ControlCode,
    SIZE_T InputBufferSizeInBytes,
    SIZE_T OutputBufferSizeInBytes
)
{
    UNREFERENCED_PARAMETER(pWdfQueue);
    UNREFERENCED_PARAMETER(InputBufferSizeInBytes);
    UNREFERENCED_PARAMETER(OutputBufferSizeInBytes);

    HRESULT hr = S_OK;

    switch (ControlCode) {
        case IOCTL_RS485_CONFIGURE_SYSTEM:
            hr = HandleConfigureSystem(pWdfRequest);
            break;

        case IOCTL_RS485_CONFIGURE_USER:
            hr = HandleConfigureUser(pWdfRequest);
            break;

        case IOCTL_RS485_REQUEST_DATA:
            hr = HandleRequestData(pWdfRequest);
            break;

        case IOCTL_RS485_RECEIVE_RESPONSE:
            hr = HandleReceiveResponse(pWdfRequest);
            break;

        case IOCTL_RS485_GET_BUFFER_STATUS:
            hr = HandleGetBufferStatus(pWdfRequest);
            break;

        case IOCTL_RS485_CLEAR_BUFFER:
            hr = HandleClearBuffer(pWdfRequest);
            break;

        case IOCTL_RS485_GET_HARDWARE_STATUS:
            hr = HandleGetHardwareStatus(pWdfRequest);
            break;

        case IOCTL_RS485_MODEL_DATA_OP:
            hr = HandleModelDataOperation(pWdfRequest);
            break;

        default:
            hr = HRESULT_FROM_WIN32(ERROR_INVALID_FUNCTION);
            break;
    }

    CompleteRequest(pWdfRequest, hr);
}

HRESULT CRS485Queue::HandleConfigureSystem(IWDFIoRequest* pRequest)
{
    RS485_COMMAND_INPUT* pInput = nullptr;
    SIZE_T inputSize = 0;

    HRESULT hr = GetInputBuffer(pRequest, (void**)&pInput, &inputSize);
    if (FAILED(hr)) {
        return hr;
    }

    if (inputSize < sizeof(RS485_COMMAND_INPUT)) {
        return E_INVALIDARG;
    }

    return m_pDevice->ProcessSystemCommand(pInput);
}

HRESULT CRS485Queue::HandleConfigureUser(IWDFIoRequest* pRequest)
{
    RS485_COMMAND_INPUT* pInput = nullptr;
    SIZE_T inputSize = 0;

    HRESULT hr = GetInputBuffer(pRequest, (void**)&pInput, &inputSize);
    if (FAILED(hr)) {
        return hr;
    }

    if (inputSize < sizeof(RS485_COMMAND_INPUT)) {
        return E_INVALIDARG;
    }

    return m_pDevice->ProcessUserCommand(pInput);
}

HRESULT CRS485Queue::HandleRequestData(IWDFIoRequest* pRequest)
{
    RS485_REQUEST_INPUT* pInput = nullptr;
    SIZE_T inputSize = 0;

    HRESULT hr = GetInputBuffer(pRequest, (void**)&pInput, &inputSize);
    if (FAILED(hr)) {
        return hr;
    }

    if (inputSize < sizeof(RS485_REQUEST_INPUT)) {
        return E_INVALIDARG;
    }

    return m_pDevice->ProcessDataRequest(pInput);
}

HRESULT CRS485Queue::HandleReceiveResponse(IWDFIoRequest* pRequest)
{
    RS485_RESPONSE_OUTPUT* pOutput = nullptr;
    SIZE_T outputSize = 0;

    HRESULT hr = GetOutputBuffer(pRequest, (void**)&pOutput, &outputSize);
    if (FAILED(hr)) {
        return hr;
    }

    if (outputSize < sizeof(RS485_RESPONSE_OUTPUT)) {
        return E_INVALIDARG;
    }

    // Get response from downlink buffer
    UINT8 responseData[RS485_PAYLOAD_SIZE];
    ULONG dataLength = 0;

    hr = m_pDevice->GetFromDownlinkBuffer(responseData, &dataLength);
    if (FAILED(hr)) {
        return hr;
    }

    if (hr == S_FALSE) {
        // No data available
        return HRESULT_FROM_WIN32(ERROR_NO_MORE_ITEMS);
    }

    // Fill output structure
    pOutput->SlaveAddress = 0; // Would extract from frame
    RtlCopyMemory(pOutput->PayloadData, responseData, RS485_PAYLOAD_SIZE);
    pOutput->DataLength = dataLength;
    RtlZeroMemory(pOutput->Reserved, sizeof(pOutput->Reserved));

    return S_OK;
}

HRESULT CRS485Queue::HandleGetBufferStatus(IWDFIoRequest* pRequest)
{
    BufferStatus* pOutput = nullptr;
    SIZE_T outputSize = 0;

    HRESULT hr = GetOutputBuffer(pRequest, (void**)&pOutput, &outputSize);
    if (FAILED(hr)) {
        return hr;
    }

    if (outputSize < sizeof(BufferStatus)) {
        return E_INVALIDARG;
    }

    return m_pDevice->GetBufferStatus(pOutput);
}

HRESULT CRS485Queue::HandleClearBuffer(IWDFIoRequest* pRequest)
{
    BufferType* pInput = nullptr;
    SIZE_T inputSize = 0;

    HRESULT hr = GetInputBuffer(pRequest, (void**)&pInput, &inputSize);
    if (FAILED(hr)) {
        return hr;
    }

    if (inputSize < sizeof(BufferType)) {
        return E_INVALIDARG;
    }

    return m_pDevice->ClearBuffers(*pInput);
}

HRESULT CRS485Queue::HandleGetHardwareStatus(IWDFIoRequest* pRequest)
{
    HardwareStatus* pOutput = nullptr;
    SIZE_T outputSize = 0;

    HRESULT hr = GetOutputBuffer(pRequest, (void**)&pOutput, &outputSize);
    if (FAILED(hr)) {
        return hr;
    }

    if (outputSize < sizeof(HardwareStatus)) {
        return E_INVALIDARG;
    }

    // Fill hardware status
    pOutput->isConnected = TRUE;
    pOutput->isDriverLoaded = TRUE;
    pOutput->signalStrength = 100;
    pOutput->errorCount = 0;
    pOutput->lastActivityTime = 0; // Would get from system time

    return S_OK;
}

HRESULT CRS485Queue::HandleModelDataOperation(IWDFIoRequest* pRequest)
{
    RS485_MODEL_DATA_INPUT* pInput = nullptr;
    SIZE_T inputSize = 0;

    HRESULT hr = GetInputBuffer(pRequest, (void**)&pInput, &inputSize);
    if (FAILED(hr)) {
        return hr;
    }

    if (inputSize < sizeof(RS485_MODEL_DATA_INPUT)) {
        return E_INVALIDARG;
    }

    return m_pDevice->ProcessModelDataOperation(pInput);
}

HRESULT CRS485Queue::GetInputBuffer(IWDFIoRequest* pRequest, void** ppBuffer, SIZE_T* pBufferSize)
{
    if (!pRequest || !ppBuffer || !pBufferSize) {
        return E_INVALIDARG;
    }

    IWDFMemory* pMemory = nullptr;
    HRESULT hr = pRequest->RetrieveInputMemory(&pMemory);
    if (FAILED(hr)) {
        return hr;
    }

    *ppBuffer = pMemory->GetDataBuffer(pBufferSize);
    pMemory->Release();

    return (*ppBuffer) ? S_OK : E_FAIL;
}

HRESULT CRS485Queue::GetOutputBuffer(IWDFIoRequest* pRequest, void** ppBuffer, SIZE_T* pBufferSize)
{
    if (!pRequest || !ppBuffer || !pBufferSize) {
        return E_INVALIDARG;
    }

    IWDFMemory* pMemory = nullptr;
    HRESULT hr = pRequest->RetrieveOutputMemory(&pMemory);
    if (FAILED(hr)) {
        return hr;
    }

    *ppBuffer = pMemory->GetDataBuffer(pBufferSize);
    pMemory->Release();

    return (*ppBuffer) ? S_OK : E_FAIL;
}

HRESULT CRS485Queue::CompleteRequest(IWDFIoRequest* pRequest, HRESULT hr, SIZE_T Information)
{
    if (!pRequest) {
        return E_INVALIDARG;
    }

    pRequest->SetInformation(Information);
    pRequest->Complete(hr);

    return S_OK;
}
